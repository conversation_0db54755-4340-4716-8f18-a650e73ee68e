import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import ACCESS_ENUM from '@/access/accessEnum'
import HomePage from '@/pages/HomePage.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomePage,
    },
    {
      path: '/about',
      name: 'about',

      component: () => import('../views/AboutView.vue'),
    },
    {
      path: '/user/login',
      name: '用户登录',

      component: () => import('../pages/user/UserLoginPage.vue'),
    },
    {
      path: '/user/register',
      name: '用户注册',

      component: () => import('../pages/user/UserRegisterPage.vue'),
    },
    {
      path: '/admin/userManage',
      name: '用户管理',
      component: () => import('../pages/admin/UserManagePage.vue'),
      meta: {
        access: ACCESS_ENUM.ADMIN,
      },
    },
    {
      path: '/noAuth',
      name: 'noAuth',
      component: () => import('../pages/NoAuthPage.vue'),
    },
    {
      path: '/add_picture',
      name: '创建图片',
      component: () => import('../pages/AddPicturePage.vue'),
    },

    {
      path: '/admin/pictureManage',
      name: '图片管理',
      component: () => import('../pages/admin/pictureManage.vue'),
      meta: {
        access: ACCESS_ENUM.ADMIN,
      },
    },
    {
      path: '/picture/:id',
      name: '图片详情',
      component: () => import('../pages/PictureDetailPage.vue'),
      props: true,
    },
    {
      path: '/add_picture/batch',
      name: '批量创建图片',
      component: () => import('../pages/admin/AddPictureBatchPage.vue'),
    },
  ],
})

export default router

<script setup lang="ts">
import { healthUsingGet } from './api/mainController'
import BasicLayout from './layouts/BasicLayout.vue'
import { useLoginUserStore } from './stores/useLoginUserStore'
import dayjs from 'dayjs'

import zhCN from 'ant-design-vue/es/locale/zh_CN'
import 'dayjs/locale/zh-cn'
dayjs.locale('zh-cn')
const locale = zhCN
healthUsingGet().then((res) => {
  console.log(res)
})

const loginUserStore = useLoginUserStore()
const loginUser = loginUserStore.fetchLoginUser()
</script>

<template>
  <a-config-provider :locale="locale">
    <BasicLayout></BasicLayout>
  </a-config-provider>
</template>

<style scoped></style>

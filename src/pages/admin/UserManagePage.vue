<template>
  <div id="userManagePage">
    <a-form layout="inline" :model="searchParams" @finish="doSearch">
      <a-form-item label="账号">
        <a-input v-model:value="searchParams.userAccount" placeholder="输入账号" allow-clear />
      </a-form-item>
      <a-form-item label="用户名">
        <a-input v-model:value="searchParams.userName" placeholder="输入用户名" allow-clear />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" html-type="submit">搜索</a-button>
      </a-form-item>
    </a-form>
    <div style="margin-bottom: 16px"></div>

    <a-table
      :columns="columns"
      :data-source="dataList"
      :pagination="pagination"
      @change="doTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'userName'">
          <div class="editable-cell">
            <div v-if="editableData[record.id]">
              <a-input v-model:value="editableData[record.id].userName" style="margin: -5px 0" />
            </div>
            <div v-else>
              {{ record.userName }}
            </div>
          </div>
        </template>

        <template v-else-if="column.dataIndex === 'userProfile'">
          <div class="editable-cell">
            <div v-if="editableData[record.id]">
              <a-input v-model:value="editableData[record.id].userProfile" style="margin: -5px 0" />
            </div>
            <div v-else>
              {{ record.userProfile }}
            </div>
          </div>
        </template>

        <template v-else-if="column.dataIndex === 'userAvatar'">
          <a>
            <a-image :src="record.userAvatar" :width="120" />
          </a>
        </template>

        <template v-else-if="column.dataIndex === 'userRole'">
          <div v-if="record.userRole === 'admin'">
            <a-tag color="green">管理员</a-tag>
          </div>
          <div v-else>
            <a-tag color="blue">普通用户</a-tag>
          </div>
        </template>

        <template v-else-if="column.dataIndex === 'createTime'">
          {{ dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss') }}
        </template>

        <template v-else-if="column.key === 'action'">
          <a-space>
            <span v-if="editableData[record.id]">
              <a @click="saveUser(record.id)">Save</a>
              <a-popconfirm title="Sure to cancel?" @confirm="cancelEdit(record.id)">
                <a>Cancel</a>
              </a-popconfirm>
            </span>
            <span v-else>
              <a-button style="margin-right: 10px" primary @click="editUser(record)">编辑</a-button>
              <a-button danger @click="deleteUser(record.id)">删除</a-button>
            </span>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
// *** IMPORT cloneDeep and updateUserUsingPost ***
import {
  deleteUserUsingPost,
  listUserPageVoUsingPost,
  updateUserUsingPost, // Assuming you will create this API function
} from '@/api/userController'
import { message } from 'ant-design-vue'
import { computed, onMounted, reactive, ref, type UnwrapRef } from 'vue'
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash-es'

const columns = [
  {
    title: 'id',
    dataIndex: 'id',
  },
  {
    title: '账号',
    dataIndex: 'userAccount',
  },
  {
    title: '用户名',
    dataIndex: 'userName',
  },
  {
    title: '头像',
    dataIndex: 'userAvatar',
  },
  {
    title: '简介',
    dataIndex: 'userProfile',
  },
  {
    title: '用户角色',
    dataIndex: 'userRole',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
  {
    title: '操作',
    key: 'action',
  },
]

const dataList = ref<API.UserVO[]>([])
const total = ref(0)
const searchParams = reactive<API.UserQueryRequest>({
  current: 1,
  pageSize: 10,
  sortOrder: 'ascend',
})

// *** ADD editableData state ***
const editableData: UnwrapRef<Record<string, API.UserVO>> = reactive({})

const pagination = computed(() => {
  return {
    current: searchParams.current,
    pageSize: searchParams.pageSize,
    total: total.value,
    showSizeChanger: true,
    showTotal: (total) => `总共 ${total} 条`,
  }
})

const fetchData = async () => {
  const res = await listUserPageVoUsingPost({
    ...searchParams,
  })
  if (res.data.code === 0 && res.data.data) {
    dataList.value = res.data.data.records ?? []
    total.value = res.data.data.total ?? 0
  } else {
    message.error('获取用户列表失败' + res.data.message)
  }
}

const doTableChange = (page: any) => {
  searchParams.current = page.current
  searchParams.pageSize = page.pageSize
  fetchData()
}

// *** IMPLEMENT editUser, saveUser, cancelEdit functions ***
const editUser = (record: API.UserVO) => {
  editableData[record.id] = cloneDeep(record)
}

const saveUser = async (userId: string) => {
  // Find the original record to merge into, to ensure all fields are present
  const originalRecord = dataList.value.find((item) => item.id === userId)
  if (!originalRecord) return

  // Merge the editable data into the original record before sending
  Object.assign(originalRecord, editableData[userId])

  // IMPORTANT: You need an API endpoint to update the user.
  // I'm assuming it's called 'updateUserUsingPost'.
  const res = await updateUserUsingPost(originalRecord)
  if (res.data.code === 0) {
    message.success('更新成功')
    // Exit edit mode
    delete editableData[userId]
    // Refresh data to be sure
    await fetchData()
  } else {
    message.error('更新失败: ' + res.data.message)
  }
}

const cancelEdit = (userId: string) => {
  delete editableData[userId]
}

const deleteUser = async (userId: string) => {
  const res = await deleteUserUsingPost({ id: userId })
  if (res.data.code === 0) {
    message.success('删除用户成功')
    fetchData()
  } else {
    message.error('删除用户失败' + res.data.message)
  }
}

const doSearch = () => {
  searchParams.current = 1
  fetchData()
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
/* Added for better alignment in edit mode */
.editable-row-operations a {
  margin-right: 8px;
}
</style>

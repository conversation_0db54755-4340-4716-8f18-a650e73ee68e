<template>
  <div id="pictureManagePage">
    <a-space>
      <a-button type="primary" href="/add_picture" target="_blank">+ 创建图片</a-button>
      <a-button type="primary" href="/add_picture/batch" target="_blank" ghost
        >+ 批量创建图片</a-button
      >
    </a-space>

    <a-form layout="inline" :model="searchParams" @finish="doSearch">
      <a-form-item label="关键词">
        <a-input
          v-model:value="searchParams.searchText"
          placeholder="从名称和关键词搜索"
          allow-clear
        />
      </a-form-item>
      <a-form-item label="类型">
        <a-input v-model:value="searchParams.category" placeholder="输入图片类型" allow-clear />
      </a-form-item>

      <a-form-item label="标签">
        <a-select
          v-model:value="searchParams.tags"
          mode="tags"
          placeholder="请输入标签"
          style="min-width: 180px"
          allow-clear
        >
        </a-select>
      </a-form-item>
      <a-form-item label="审核状态">
        <a-select
          v-model:value="searchParams.reviewStatus"
          placeholder="请选择审核状态"
          style="min-width: 180px"
          :options="PIC_REVIEW_STATUS_OPTIONS"
          allow-clear
        >
        </a-select>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" html-type="submit">搜索</a-button>
      </a-form-item>
    </a-form>
    <div style="margin-bottom: 16px"></div>

    <a-table
      :columns="columns"
      :data-source="dataList"
      :pagination="pagination"
      @change="doTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'id'">
          <div>
            {{ record.id }}
          </div>
        </template>

        <template v-else-if="column.dataIndex === 'url'">
          <a-image :src="record.url" :width="120" />
        </template>

        <template v-else-if="column.dataIndex === 'name'">
          {{ record.name }}
        </template>

        <template v-else-if="column.dataIndex === 'introduction'">
          {{ record.introduction }}
        </template>

        <template v-else-if="column.dataIndex === 'category'">
          {{ record.category }}
        </template>

        <template v-else-if="column.dataIndex === 'tags'">
          <a-space wrap>
            <a-tag v-for="tag in JSON.parse(record.tags || '[]')">
              {{ tag }}
            </a-tag>
          </a-space>
        </template>

        <template v-else-if="column.dataIndex === 'picInfo'">
          <div>格式：{{ record.picFormat }}</div>
          <div>宽度：{{ record.picWidth }}</div>
          <div>高度：{{ record.picHeight }}</div>
          <div>宽高比：{{ record.picScale }}</div>
          <div>大小：{{ (record.picSize / 1024).toFixed(2) }}KB</div>
        </template>

        <template v-else-if="column.dataIndex === 'userId'">
          {{ record.userId }}
        </template>
        <!-- 审核信息 -->
        <template v-if="column.dataIndex === 'reviewMessage'">
          <div>审核状态：{{ PIC_REVIEW_STATUS_MAP[record.reviewStatus] }}</div>
          <div>审核信息：{{ record.reviewMessage }}</div>
          <div>审核人：{{ record.reviewerId }}</div>
        </template>

        <template v-else-if="column.dataIndex === 'createTime'">
          {{ dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss') }}
        </template>

        <template v-else-if="column.dataIndex === 'editTime'">
          {{ dayjs(record.editTime).format('YYYY-MM-DD HH:mm:ss') }}
        </template>

        <template v-else-if="column.key === 'action'">
          <a-space wrap>
            <span v-if="editableData[record.id]">
              <a @click="savePicture(record.id)">Save</a>
              <a-popconfirm title="Sure to cancel?" @confirm="cancelEdit(record.id)">
                <a>Cancel</a>
              </a-popconfirm>
            </span>
            <span v-else>
              <a-space wrap>
                <a-button
                  v-if="record.reviewStatus !== PIC_REVIEW_STATUS_ENUM.PASS"
                  type="link"
                  @click="handleReview(record, PIC_REVIEW_STATUS_ENUM.PASS)"
                >
                  通过
                </a-button>
                <a-button
                  v-if="record.reviewStatus !== PIC_REVIEW_STATUS_ENUM.REJECT"
                  type="link"
                  danger
                  @click="handleReview(record, PIC_REVIEW_STATUS_ENUM.REJECT)"
                >
                  拒绝
                </a-button>
                <a-button type="link" :href="`/add_picture?id=${record.id}`" target="_blank">
                  编辑</a-button
                >
              </a-space>

              <a-button type="link" danger @click="deletePicture(record.id)">删除</a-button>
            </span>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
// *** IMPORT cloneDeep and updatePictureUsingPost ***
import {
  deletePictureUsingPost,
  doPictureReciewUsingPost,
  listPictureTagCategoryUsingGet,
  listPictureUsingPost,
  listPictureVoUsingPost,
  updatePictureUsingPost, // Assuming you will create this API function
} from '@/api/pictureController'
import { message } from 'ant-design-vue'
import { computed, onMounted, reactive, ref, type UnwrapRef } from 'vue'
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash-es'
import {
  PIC_REVIEW_STATUS_ENUM,
  PIC_REVIEW_STATUS_MAP,
  PIC_REVIEW_STATUS_OPTIONS,
} from '@/constants/picture'
const columns = [
  {
    title: 'id',
    dataIndex: 'id',
    width: 80,
  },
  {
    title: '图片',
    dataIndex: 'url',
  },
  {
    title: '名称',
    dataIndex: 'name',
  },
  {
    title: '简介',
    dataIndex: 'introduction',
    ellipsis: true,
  },
  {
    title: '类型',
    dataIndex: 'category',
  },
  {
    title: '标签',
    dataIndex: 'tags',
  },
  {
    title: '图片信息',
    dataIndex: 'picInfo',
  },
  {
    title: '用户 id',
    dataIndex: 'userId',
    width: 80,
  },
  {
    title: '审核信息',
    dataIndex: 'reviewMessage',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
  {
    title: '编辑时间',
    dataIndex: 'editTime',
  },
  {
    title: '操作',
    key: 'action',
  },
]
const tags = ref<String[]>([])
const dataList = ref<API.Picture[]>([])
const total = ref(0)
const searchParams = reactive<API.PictureQueryRequest>({
  current: 1,
  pageSize: 10,
  sortOrder: 'ascend',
})

// *** ADD editableData state ***
const editableData: UnwrapRef<Record<string, API.Picture>> = reactive({})

const pagination = computed(() => {
  return {
    current: searchParams.current,
    pageSize: searchParams.pageSize,
    total: total.value,
    showSizeChanger: true,
    showTotal: (total) => `总共 ${total} 条`,
  }
})

const fetchData = async () => {
  console.log(searchParams.tags)
  const res = await listPictureUsingPost({
    ...searchParams,
  })
  if (res.data.code === 0 && res.data.data) {
    dataList.value = res.data.data.records ?? []
    total.value = res.data.data.total ?? 0
  } else {
    message.error('获取图片列表失败' + res.data.message)
  }
}
const handleReview = async (record: API.Picture, reviewStatus: number) => {
  const reviewMessage =
    reviewStatus === PIC_REVIEW_STATUS_ENUM.PASS ? '管理员操作通过' : '管理员操作拒绝'
  const res = await doPictureReciewUsingPost({
    id: record.id,
    reviewStatus,
    reviewMessage,
  })
  if (res.data.code === 0) {
    message.success('审核操作成功')
    // 重新获取列表
    fetchData()
  } else {
    message.error('审核操作失败，' + res.data.message)
  }
}

// const fetchTags = async () => {
//   const res = await listPictureTagCategoryUsingGet()
//   if (res.data.code == 0 && res.data.data) {
//     tags.value = res.data.data.tagList
//   }
// }

const doTableChange = (page: any) => {
  searchParams.current = page.current
  searchParams.pageSize = page.pageSize
  fetchData()
}

// *** IMPLEMENT editPicture, savePicture, cancelEdit functions ***
const editPicture = (record: API.Picture) => {
  editableData[record.id] = cloneDeep(record)
}

const savePicture = async (pictureId: string) => {
  // Find the original record to merge into, to ensure all fields are present
  const originalRecord = dataList.value.find((item) => item.id === pictureId)
  if (!originalRecord) return

  // Merge the editable data into the original record before sending
  Object.assign(originalRecord, editableData[pictureId])

  // IMPORTANT: You need an API endpoint to update the picture.
  // I'm assuming it's called 'updatePictureUsingPost'.
  const res = await updatePictureUsingPost(originalRecord)
  if (res.data.code === 0) {
    message.success('更新成功')
    // Exit edit mode
    delete editableData[pictureId]
    // Refresh data to be sure
    await fetchData()
  } else {
    message.error('更新失败: ' + res.data.message)
  }
}

const cancelEdit = (pictureId: string) => {
  delete editableData[pictureId]
}

const deletePicture = async (pictureId: string) => {
  const res = await deletePictureUsingPost({ id: pictureId })
  if (res.data.code === 0) {
    message.success('删除图片成功')
    fetchData()
  } else {
    message.error('删除图片失败' + res.data.message)
  }
}

const doSearch = () => {
  searchParams.current = 1
  fetchData()
}

onMounted(() => {
  fetchData()
  // fetchTags()
})
</script>

<style scoped>
/* Added for better alignment in edit mode */
.editable-row-operations a {
  margin-right: 8px;
}
</style>

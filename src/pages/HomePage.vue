<template>
  <div id="homePage">
    <div class="search-bar">
      <a-input-search
        placeholder="从海量图片中搜索"
        v-model:value="searchParams.searchText"
        enter-button="搜索"
        size="large"
        @search="doSearch"
      />
    </div>
    <div>
      <!-- 分类 + 标签 -->
      <a-tabs v-model:activeKey="selectedCategory" @change="doSearch">
        <a-tab-pane key="all" tab="全部" />
        <a-tab-pane v-for="category in categoryList" :key="category" :tab="category" />
      </a-tabs>
      <div class="tag-bar" style="margin-bottom: 16px">
        <span style="margin-right: 8px">标签：</span>
        <a-space :size="[0, 8]" wrap>
          <a-checkable-tag
            v-for="(tag, index) in tagList"
            :key="tag"
            v-model:checked="selectedTagList[index]"
            @change="doSearch"
          >
            {{ tag }}
          </a-checkable-tag>
        </a-space>
      </div>
    </div>
    <a-list
      :grid="{ gutter: 16, xs: 1, sm: 2, md: 4, lg: 4, xl: 6, xxl: 3 }"
      :data-source="dataList"
      :pagination="pagination"
      :loading="loading"
    >
      <template #renderItem="{ item: picture }">
        <a-list-item style="padding: 0" @click="doClickPicture(picture)">
          <!-- 单张图片 -->
          <a-card hoverable>
            <template #cover>
              <img
                style="height: 180px; object-fit: cover"
                :alt="picture.name"
                :src="picture.thumbnailUrl ?? picture.url"
              />
            </template>
            <a-card-meta :title="picture.name">
              <template #description>
                <a-flex>
                  <a-tag color="green">
                    {{ picture.category ?? '默认' }}
                  </a-tag>
                  <a-checkable-tag v-for="tag in picture.tags" :key="tag">
                    {{ tag }}
                  </a-checkable-tag>
                </a-flex>
              </template>
            </a-card-meta>
          </a-card>
        </a-list-item>
      </template>
    </a-list>
  </div>
</template>

<script lang="ts" setup>
import { listPictureTagCategoryUsingGet, listPictureVoUsingPost } from '@/api/pictureController'
import { message } from 'ant-design-vue'
import { ref, reactive, computed, type UnwrapRef, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const dataList = ref<API.PictureVO[]>([])
const total = ref(0)
const loading = ref(true)
const searchParams = reactive<API.PictureQueryRequest>({
  current: 1,
  pageSize: 10,
  sortOrder: 'descend',
  sortField: 'createTime',
})

const pagination = computed(() => {
  return {
    current: searchParams.current,
    pageSize: searchParams.pageSize,
    total: total.value,
    onChange: (page: number, pageSize: number) => {
      searchParams.current = page
      searchParams.pageSize = pageSize
      fetchData()
    },
  }
})
const categoryList = ref<string[]>([])
const tagList = ref<string[]>([])
const selectedCategory = ref<string>('all')
const selectedTagList = ref<boolean[]>([])

const fetchCategoryAndTags = async () => {
  // 假设有 API 获取分类和标签
  const res = await listPictureTagCategoryUsingGet()
  if (res.data.code == 0 && res.data.data) {
    categoryList.value = res.data.data.categoryList ?? []

    tagList.value = res.data.data.tagList ?? []
  } else {
    message.error('加载分类标签失败' + res.data.message)
  }
}

const fetchData = async () => {
  loading.value = true
  //转换标签和分类参数
  const params = {
    ...searchParams,
    tags: [],
  }
  if (selectedCategory.value === 'all') {
    params.category = undefined // 清除分类筛选条件
  } else {
    params.category = selectedCategory.value
  }

  selectedTagList.value.forEach((flag, index) => {
    if (flag) {
      params.tags.push(tagList.value[index])
    }
  })

  const res = await listPictureVoUsingPost(params)
  if (res.data.code === 0 && res.data.data) {
    dataList.value = res.data.data.records ?? []
    total.value = res.data.data.total ?? 0
    loading.value = false
  } else {
    message.error('获取图片列表失败' + res.data.message)
  }
}
const doSearch = () => {
  searchParams.current = 1

  fetchData()
}
const router = useRouter()
// 跳转至图片详情
const doClickPicture = (picture) => {
  router.push({
    path: `/picture/${picture.id}`,
  })
}

onMounted(() => {
  fetchData()
  fetchCategoryAndTags()
})
</script>

<style scoped>
#homePage {
}

#homePage .search-bar {
  margin: 0 auto 16px;
  max-width: 480px;
}
</style>

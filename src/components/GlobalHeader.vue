<template>
  <div id="globalHeader">
    <a-row :wrap="false" align="middle">
      <a-col flex="200px">
        <router-link to="/">
          <div class="title-bar">
            <img class="logo" src="../assets/icon.png" alt="logo" />
            <div class="title">云图库</div>
          </div>
        </router-link>
      </a-col>
      <a-col flex="auto">
        <a-menu
          v-model:selectedKeys="current"
          mode="horizontal"
          :items="menuItems"
          @click="doMenuClick"
        />
      </a-col>
      <a-col flex="100px">
        <div class="user-login-status">
          <div
            v-if="
              loginUserStore.loginUser?.userRole && loginUserStore.loginUser.userRole !== 'notLogin'
            "
          >
            <a-dropdown>
              <a-space style="cursor: pointer">
                <a-avatar :size="32" :src="loginUserStore.loginUser.userAvatar"></a-avatar>
                <span>{{ loginUserStore.loginUser.userName ?? '无名' }}</span>
              </a-space>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="logout" @click="doLogout">
                    <LoginOutlined />
                    退出登录
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
          <div v-else>
            <a-button type="primary" href="/user/login">登录</a-button>
          </div>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" setup>
import { computed, h, ref } from 'vue'
// 1. ✅ 导入默认导出的 router 实例
import router from '@/router/index'
import { HomeOutlined, LoginOutlined } from '@ant-design/icons-vue'
import { type MenuProps, message } from 'ant-design-vue'
import { useLoginUserStore } from '@/stores/useLoginUserStore'
import { userLogoutUsingPost } from '@/api/userController'
import checkAccess from '@/access/checkAccess'
import { User } from '@/api/models'

// 原始菜单项定义：这里的 key 应该与路由配置中的 path 一致
const originItems: MenuProps['items'] = [
  {
    key: '/',
    icon: () => h(HomeOutlined),
    label: '主页',
    title: '主页',
  },

  {
    key: '/admin/userManage',
    label: '用户管理',
    title: '用户管理',
  },
  {
    key: '/admin/pictureManage',
    label: '图片管理',
    title: '图片管理',
  },
  {
    key: '/add_picture',
    label: '添加图片',
    title: '添加图片',
  },

  {
    key: '/about',
    label: '关于',
    title: '关于',
  },
]

const loginUserStore = useLoginUserStore()

/**
 * 动态计算菜单项
 */
const menuItems = computed(() => {
  // 2. ✅ 调用 router.getRoutes() 方法获取所有已注册的路由记录
  const allRoutes = router.getRoutes()

  return originItems.filter((menu) => {
    // 3. ✅ 在 router.getRoutes() 返回的数组中查找匹配项
    const route = allRoutes.find((r) => r.path === menu.key)

    // 如果路由不存在或配置了 hideInMenu，则隐藏
    if (!route || route.meta?.hideInMenu) {
      return false
    }

    // 根据权限进行最终过滤
    return checkAccess(loginUserStore.loginUser, route.meta?.access as string)
  })
})

/**
 * 菜单路由跳转
 */
const doMenuClick = ({ key }: { key: string }) => {
  router.push({
    path: key,
  })
}

/**
 * 动态高亮当前菜单项
 */
const current = ref<string[]>([])
router.afterEach((to) => {
  current.value = [to.path]
})

/**
 * 用户注销
 */
const doLogout = async () => {
  try {
    const res = await userLogoutUsingPost()
    if (res.data.code === 0) {
      loginUserStore.setLoginUser({ userRole: 'notLogin' })
      message.success('退出成功')
      router.push({ path: '/user/login', replace: true })
    } else {
      message.error('退出失败：' + res.data.message)
    }
  } catch (error) {
    message.error('退出失败，请稍后重试')
  }
}
</script>

<style scoped>
#globalHeader {
  box-shadow: #eee 1px 1px 5px;
}

#globalHeader .title-bar {
  display: flex;
  align-items: center;
  height: 64px;
}

.title {
  color: black;
  font-size: 18px;
  margin-left: 16px;
}

.logo {
  height: 48px;
}

.user-login-status {
  text-align: right;
  padding-right: 16px;
}
</style>

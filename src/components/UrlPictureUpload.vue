<template>
  <div class="url-picture-upload">
    <div class="picture-continer">
      <img v-if="picture?.url" :src="picture?.url" alt="avatar" />
    </div>

    <a-input-group compact>
      <a-input v-model:value="pictureUrl" style="width: calc(100% - 120px)" allow-clear />
      <a-button type="primary" @click="handleUpload" style="width: 120px">获取</a-button>
    </a-input-group>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { PlusOutlined, LoadingOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import type { UploadChangeParam, UploadProps } from 'ant-design-vue'
import { uploadPictureByUrlUsingPost, uploadPictureUsingPost } from '@/api/pictureController'

interface Props {
  picture?: API.PictureVO
  onSuccess?: (newPicture: API.PictureVO) => void
}

const props = defineProps<Props>()
const loading = ref<boolean>(false)
const pictureUrl = ref<string>('')

const handleUpload = async () => {
  const params: API.PictureUploadRequest = {
    pictureUrl: pictureUrl.value,
  }
  if (props.picture) {
    params.id = props.picture.id
  }
  loading.value = true
  try {
    const res = await uploadPictureByUrlUsingPost(params)
    if (res.data.code == 0 && res.data.data) {
      message.success('图片上传成功')
      props.onSuccess?.(res.data.data)
    } else {
      message.error('图片上传失败' + res.data.message)
    }
  } catch (error) {
    message.error('图片上传失败' + error.message)
    console.error('图片上传失败:', error)
  }
  loading.value = false
}

const beforeUpload = (file: UploadProps['fileList'][number]) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
  if (!isJpgOrPng) {
    message.error('只能上传 JPG 或 PNG 格式的图片!')
  }
  const isLt3M = file.size / 1024 / 1024 < 3
  if (!isLt3M) {
    message.error('图片大小必须小于3MB!')
  }
  return isJpgOrPng && isLt3M
}
</script>
<style scoped>
.picture-upload :deep(.ant-upload) {
  width: 100% !important;
  height: 100% !important;
  min-width: 152px;
  min-height: 152px;
}

.avatar-uploader > .ant-upload {
  width: 128px;
  height: 128px;
}
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}

.picture-upload img {
  max-width: 100%;
  max-height: 480px;
}

.url-picture-upload .picture-continer {
  text-align: center;
}
</style>
